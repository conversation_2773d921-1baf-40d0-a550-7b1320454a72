@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap');

:root{

    --btn-color: #fdfdfd;/* button color*/
    --btn-bg: #0d0d0d;/* button bg color*/

    --primary-text-color: #000;
    --header-link-hover: #dddcdccf;
    --footer-link-hover: #6dbb00;
    --input-focus-bd-color: #0d0d0d;
}


html {
    scroll-behavior: smooth;
    font-family: "Roboto", sans-serif;
}

header{
    /* background-color: #fff;
    color: #000; */
}


header > .collapsible-header{
    display: flex;
    gap: 1rem;
    width: 100%;
    background-color: inherit;
    place-content: center;
    overflow: hidden;
    transition: width 0.3s ease;
}

.outline-effect {
    color: black;
    -webkit-text-fill-color: transparent; /* Will override color (regardless of order) */
    -webkit-text-stroke: 2px black;
}

.animated-collapse{
    transition: width 0.3s ease;
}

.header-links {
    display: flex;
    align-items: center;
    min-width: fit-content;
    border-radius: 25px;
    padding: 5px 15px;
    transition: background-color 0.5s, color 0.5s;
}


.header-links:hover {
    background-color: var(--header-link-hover);
}

.material-icons.md-40 {
    font-size: 40px;
}

.primary-text-color{
    color: var(--primary-text-color);
}

.opacity-0{
    opacity: 0 !important;
}

.opacity-100{
    opacity: 100 !important;
}

.btn{
    padding: 10px 15px;
    width: max-content;
    border-radius: 25px;
    color: var(--btn-color);
    background-color: var(--btn-bg);
    justify-content: center;
    align-items: center;
    display: flex;
    cursor: pointer;
}

.btn:hover{

}

.btn:disabled{
    cursor: default;
}

.input{
    padding: 10px;
    background-color: transparent;
    border-radius: 25px;
    /* outline: none; */
    min-width: 100px;
    border: 2px solid #818080;
    transition: border 0.3s;
}

.input:active, .input:focus, .input:focus-within{
    border: 2px solid var(--input-focus-bd-color);
}

.input-error{
    border-bottom: 3px solid #ff1e1e;
}

.input-error:focus-within{
    border-bottom: 3px solid #fd0101;
}


/* Navigation dots styling */
.dots-container {
    text-align: center;
    margin-top: 20px;
}

.footer-link{
    color: #0d0d0d;
    transition: color 0.3s;
}

.footer-link:hover{
    color: var(--footer-link-hover);
}


.review-container {
    position: relative;
    max-width: 600px;
    margin: auto;
}

.review-card{
    box-shadow: 0px 2px 4px #757474a0;
    border-radius: 15px;
    /* width: 200px; */
    /* height: 550px; */
    padding: 10px;
}

/* --------- collapsible div ---------- */
.collapsible {
    background-color: #f3f0f0;
    color: #2b2929;
    /* cursor: pointer; */
    padding: 5px;
    width: 100%;
    border: none;
    text-align: left;
    outline: none;
    font-size: 16px;
    transition: 0.4s;
}

/* Style for the collapsible content */
.content {
    padding: 0 18px;
    /* display: none; */
    height: 0px;
    overflow: hidden;
    background-color: transparent;
    transition: height 0.5s;
    text-align: justify;
    margin-top: 10px;
}

.collapsible .active,
.collapsible:hover {
    /* background-color: #dedddd; */
}

@media not all and (min-width: 1024px) {
    header .collapsible-header {
        position: fixed;
        right: 0px;
        flex-direction: column;
        opacity: 0;
        height: 100vh;
        min-height: 100vh;
        height: 100dvh;
        width: 0vw;
        justify-content: space-between;
        padding: 5px;
        padding-top: 5%;
        padding-bottom: 5%;
        place-items: end;
        background-color: #ffffff;
        color: #000000;
        overflow-y: auto;
        box-shadow: 2px 0px 3px #000;
    }

    .header-links{
        color: black;
    }

}

/* ---------- Contact form (no Tailwind) ---------- */
.contact-wrapper {
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
}
.contact-wrapper h3 {
    text-align: center;
    font-size: 28px;
    margin-bottom: 20px;
}
.contact-form {
    background: #f7f7f7;
    border: 1px solid #e2e2e2;
    border-radius: 12px;
    padding: 20px;
}
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    margin-bottom: 1rem;
}
.form-field {
    display: flex;
    flex-direction: column;
    gap: 6px;
}
.form-field label {
    font-weight: 500;
}
.form-field textarea.input {
    min-height: 140px;
    resize: vertical;
}
.form-actions {
    margin-top: 16px;
    display: flex;
    justify-content: flex-end;
}
@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
    }
    .contact-wrapper {
        padding: 10px;
    }
}