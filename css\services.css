/* Service Pages Styling */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: "Roboto", sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #fff;
}

/* Hero Section */
.hero-container {
    position: relative;
    width: 100%;
    padding: 24px;
    padding-top: 24px;
}

.back-button {
    position: absolute;
    top: 32px;
    left: 32px;
    z-index: 10;
}

.hero-image-wrapper {
    position: relative;
    height: 280px;
    border-radius: 8px;
    overflow: hidden;
}

.hero-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Main Content */
.main-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 48px 24px;
}

.page-header {
    text-align: center;
    margin-bottom: 48px;
}

.page-title {
    font-size: 3rem;
    font-weight: bold;
    margin-bottom: 16px;
    color: #1a1a1a;
}

.page-subtitle {
    font-size: 1.25rem;
    color: #666;
    max-width: 768px;
    margin: 0 auto;
}

/* Content Grid */
.content-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 48px;
    margin-top: 48px;
}

.content-section {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.section-title {
    font-size: 1.875rem;
    font-weight: 600;
    margin-bottom: 16px;
}

.section-title.green {
    color: #16a34a;
}

.section-title.blue {
    color: #2563eb;
}

.section-text {
    color: #4b5563;
    line-height: 1.7;
    margin-bottom: 16px;
}

.section-list {
    list-style: disc;
    margin-left: 24px;
    color: #4b5563;
}

.section-list li {
    margin-bottom: 8px;
    line-height: 1.6;
}

/* Feature Cards */
.feature-section {
    background-color: #f9fafb;
    padding: 48px;
    border-radius: 8px;
    margin-top: 48px;
}

.feature-title {
    font-size: 1.875rem;
    font-weight: 600;
    text-align: center;
    margin-bottom: 32px;
}

.feature-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 32px;
}

.feature-card {
    text-align: center;
}

.feature-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 8px;
}

.feature-card p {
    color: #6b7280;
    line-height: 1.6;
}

/* Order Button */
.order-section {
    text-align: center;
    margin: 48px 0;
}

/* Other Services Section */
.other-services {
    margin-top: 64px;
}

.other-services-title {
    font-size: 1.875rem;
    font-weight: 600;
    text-align: center;
    margin-bottom: 32px;
}

.services-container {
    max-width: 1024px;
    margin: 0 auto;
}

.services-row-3 {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 24px;
    margin-bottom: 24px;
}

.services-row-2 {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 24px;
    max-width: 512px;
    margin: 0 auto;
}

.service-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: box-shadow 0.3s ease;
    text-decoration: none;
    color: inherit;
}

.service-card:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.service-image {
    width: 100%;
    height: 180px;
    object-fit: cover;
}

.service-content {
    padding: 16px;
}

.service-name {
    font-weight: 600;
    font-size: 1.125rem;
    margin-bottom: 8px;
}

.service-description {
    color: #6b7280;
    font-size: 0.875rem;
    line-height: 1.5;
}

/* Footer */
.page-footer {
    margin-top: auto;
    padding: 24px;
    text-align: center;
    font-size: 0.875rem;
    color: #6b7280;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-container {
        padding: 16px;
    }
    
    .hero-image-wrapper {
        height: 240px;
    }
    
    .back-button {
        top: 24px;
        left: 24px;
    }
    
    .page-title {
        font-size: 2rem;
    }
    
    .content-grid {
        grid-template-columns: 1fr;
        gap: 32px;
    }
    
    .feature-section {
        padding: 32px 24px;
    }
    
    .feature-grid {
        grid-template-columns: 1fr;
        gap: 24px;
    }
    
    .services-row-3 {
        grid-template-columns: 1fr;
    }
    
    .services-row-2 {
        grid-template-columns: 1fr;
        max-width: 100%;
    }
}

@media (max-width: 1024px) and (min-width: 769px) {
    .services-row-3 {
        grid-template-columns: repeat(2, 1fr);
    }
}
